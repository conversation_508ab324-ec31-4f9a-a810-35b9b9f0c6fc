import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchReceivingDetail } from '../../apiHandling/StockAPI/fetchReceivingDetailAPI';
import { fetchBranchDetails } from '../../apiHandling/StockAPI/fetchBranchDetailsAPI';

const ViewReceivingScreen = ({ route }) => {
    const navigation = useNavigation();
    const { grId, docId } = route.params;

    // State for receiving data
    const [receivingData, setReceivingData] = useState(null);
    const [branchData, setBranchData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    useEffect(() => {
        fetchReceivingData();
    }, []);

    const fetchReceivingData = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Fetch receiving details
            const receivingDetail = await fetchReceivingDetail(bearerToken, loginBranchID, grId);
            setReceivingData(receivingDetail);

            // Fetch branch details
            const branchDetail = await fetchBranchDetails(bearerToken, loginBranchID);
            if (branchDetail && branchDetail.length > 0) {
                setBranchData(branchDetail[0]);
            }
        } catch (error) {
            console.error('Error fetching receiving data:', error);
            Alert.alert('Error', 'Failed to fetch receiving data');
        } finally {
            setLoading(false);
        }
    };

    const formatBusinessDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB');
    };

    const formatPrintDate = () => {
        const now = new Date();
        return now.toLocaleDateString('en-GB') + ' ' + now.toLocaleTimeString('en-GB', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    };

    const calculateTotals = () => {
        if (!receivingData || !receivingData.grDetails) {
            return { totalQty: 0 };
        }

        const totalQty = receivingData.grDetails.reduce((sum, item) => {
            return sum + (parseFloat(item.Qty) || 0);
        }, 0);

        return { totalQty };
    };

    // Bluetooth printer functions
    const scanForPrinters = async () => {
        setIsScanning(true);
        try {
            const printers = await BLEPrinter.getDeviceList();
            setAvailablePrinters(printers);
        } catch (error) {
            console.error('Error scanning for printers:', error);
            Alert.alert('Error', 'Failed to scan for printers');
        } finally {
            setIsScanning(false);
        }
    };

    const connectToPrinter = async (printer) => {
        try {
            await BLEPrinter.connectPrinter(printer.inner_mac_address);
            setSelectedPrinter(printer);
            Alert.alert('Success', `Connected to ${printer.device_name}`);
        } catch (error) {
            console.error('Error connecting to printer:', error);
            Alert.alert('Error', 'Failed to connect to printer');
        }
    };

    const printReceiving = async () => {
        if (!selectedPrinter) {
            Alert.alert('Error', 'Please select a printer first');
            return;
        }

        if (!receivingData || !receivingData.grHeader || !receivingData.grDetails) {
            Alert.alert('Error', 'No receiving data to print');
            return;
        }

        setIsPrinting(true);
        try {
            const header = receivingData.grHeader?.[0] || {};
            const details = receivingData.grDetails || [];
            const totals = calculateTotals();

            // Build receipt content
            let receiptContent = '';
            
            // Header
            receiptContent += '[C]<b>ABIS EXPORTS INDIA PVT LTD</b>\n';
            receiptContent += `[C]${branchData?.BranchName || ''}\n`;
            receiptContent += `[C]${branchData?.Address1 || ''}\n`;
            if (branchData?.Address2) receiptContent += `[C]${branchData.Address2}\n`;
            receiptContent += `[C]${branchData?.City || ''} ${branchData?.PinCode || ''}\n`;
            receiptContent += `[C]Phone: ${branchData?.Phone || ''}\n`;
            receiptContent += '[C]<<RECEIVING>>\n';
            receiptContent += '--------------------------------\n';
            
            // Receiving details
            receiptContent += `GRNO :${header.GRId || docId}       GR Date: ${formatBusinessDate(header.GRDate)}\n`;
            receiptContent += `Branch :${branchData?.BranchId || ''}/${branchData?.BranchName || ''}\n`;
            receiptContent += `Vendor :${header.VendorID || ''}/${header.vendorName || ''}\n`;
            receiptContent += `Invoice No :${header.VendorInvoiceNo || ''}\n`;
            receiptContent += `Vehicle No :${header.VehicleNumber || 'NIL'}\n`;
            receiptContent += `Printed On : ${formatPrintDate()}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += 'ItemCode  Item Name\n';
            receiptContent += '                            Qty\n';
            receiptContent += '----------------------------------------\n';
            
            // Items
            details.forEach(item => {
                receiptContent += `${item.ItemID || ''}  ${item.ItemName || ''}\n`;
                receiptContent += `                             ${item.Qty || 0}\n`;
            });
            
            receiptContent += '----------------------------------------\n';
            receiptContent += `TOTAL :                      ${totals.totalQty}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += `Remarks : ${header.Remarks || ''}\n`;

            await BLEPrinter.printText(receiptContent);
            Alert.alert('Success', 'Receiving printed successfully!');
        } catch (error) {
            console.error('Error printing receiving:', error);
            Alert.alert('Error', 'Failed to print receiving');
        } finally {
            setIsPrinting(false);
        }
    };

    if (loading) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <ActivityIndicator size="large" color="#02096A" />
                <Text style={{ marginTop: 10 }}>Loading receiving data...</Text>
            </View>
        );
    }

    if (!receivingData || !receivingData.grHeader || !receivingData.grDetails) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>No receiving data found.</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={{ color: 'blue', marginTop: 10 }}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const header = receivingData.grHeader[0];
    const { totalQty } = calculateTotals();

    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>

            <Text style={styles.title}>ABIS EXPORTS INDIA PVT LTD</Text>
            <Text style={styles.center}>{branchData?.BranchName || ''}</Text>
            <Text style={styles.center}>{branchData?.AreaName || ''}</Text>
            <Text style={styles.center}>{branchData?.PINCODE || ''}</Text>
            <Text style={styles.center}>PhoneNo :</Text>
            <Text style={styles.center}>{'<<RECEIVING>>'}</Text>

            <View style={styles.rowSpaceBetween}>
                <Text>GRNO : {header.GRId || docId}</Text>
                <Text>GR Date: {formatBusinessDate(header.GRDate)}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Branch: {branchData?.BranchId}/{branchData?.BranchName}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Vendor: {header.VendorID || ''}/{header.vendorName || ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Invoice No: {header.VendorInvoiceNo || ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Vehicle No: {header.VehicleNumber || 'NIL'}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Printed On: {formatPrintDate()}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={[styles.cell, styles.itemCodeCell]}>ItemCode</Text>
                    <Text style={[styles.cell, styles.itemNameCell]}>Item Name</Text>
                    <Text style={[styles.cell, styles.qtyCell]}>QTY</Text>
                </View>
                {receivingData.grDetails.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={[styles.cell, styles.itemCodeCell]}>{item.ItemID}</Text>
                        <Text style={[styles.cell, styles.itemNameCell]}>{item.ItemName}</Text>
                        <Text style={[styles.cell, styles.qtyCell]}>{parseFloat(item.Qty) || 0}</Text>
                    </View>
                ))}
            </View>

            <View style={styles.divider} />

            <View style={styles.totalRow}>
                <Text style={styles.totalText}>TOTAL: {totalQty}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.remarksText}>Remarks: {header.Remarks || ''}</Text>

            {/* Bluetooth Printer Section */}
            <View style={styles.printerSection}>
                <Text style={styles.sectionHeader}>Bluetooth Printer:</Text>
                
                <View style={styles.printerControls}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            data={availablePrinters.map(printer => ({
                                label: printer.device_name,
                                value: printer
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={(item) => connectToPrinter(item.value)}
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                        />
                    </View>
                    
                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={scanForPrinters}
                        disabled={isScanning}
                    >
                        <Icon name="refresh" size={20} color="#fff" />
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printReceiving}
                        disabled={!selectedPrinter || isPrinting}
                    >
                        <Icon name="print" size={20} color="#fff" />
                        <Text style={styles.buttonText}>Print</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
        padding: 16,
    },
    backButtonContainer: {
        marginBottom: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 8,
    },
    center: {
        textAlign: 'center',
        marginBottom: 4,
        fontSize: 14,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    rowRight: {
        alignItems: 'flex-end',
        marginBottom: 4,
    },
    divider: {
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        marginVertical: 12,
    },
    sectionHeader: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    table: {
        borderWidth: 1,
        borderColor: '#ccc',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        paddingVertical: 8,
    },
    cell: {
        paddingHorizontal: 8,
        fontSize: 12,
    },
    itemCodeCell: {
        flex: 2,
    },
    itemNameCell: {
        flex: 4,
    },
    qtyCell: {
        flex: 1,
        textAlign: 'right',
    },
    totalRow: {
        alignItems: 'flex-end',
        marginVertical: 8,
    },
    totalText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    remarksText: {
        fontSize: 14,
        marginBottom: 16,
    },
    printerSection: {
        marginTop: 20,
        marginBottom: 20,
    },
    printerControls: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default ViewReceivingScreen;
